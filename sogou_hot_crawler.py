#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜狗热榜爬虫
基于提供的HTML结构分析编写
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from datetime import datetime
import os

# 可选的Selenium支持
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium未安装，将使用requests方式。如需更强的反爬能力，请安装: pip install selenium")


class SogouHotCrawler:
    def __init__(self):
        # 多个User-Agent轮换
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        self.base_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }

        self.session = requests.Session()
        self.session.verify = False  # 忽略SSL证书验证
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    def get_random_headers(self):
        """获取随机请求头"""
        headers = self.base_headers.copy()
        headers['User-Agent'] = random.choice(self.user_agents)
        return headers

    def get_hot_list(self, url, max_retries=3):
        """
        爬取热榜数据
        """
        for attempt in range(max_retries):
            try:
                print(f"第 {attempt + 1} 次尝试爬取...")

                # 随机延时，避免被反爬
                time.sleep(random.uniform(2, 5))

                # 使用随机请求头
                headers = self.get_random_headers()

                # 添加Referer
                if 'tophub.today' in url:
                    headers['Referer'] = 'https://tophub.today/'

                response = self.session.get(url, headers=headers, timeout=15)

                print(f"响应状态码: {response.status_code}")

                if response.status_code == 403:
                    print("遇到403错误，尝试更换策略...")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(5, 10))  # 更长的等待时间
                        continue

                response.raise_for_status()
                response.encoding = 'utf-8'
            
                # 检查是否遇到验证页面
                if '安全验证' in response.text or '验证码' in response.text:
                    print("遇到安全验证页面，需要人工处理")
                    return []

                soup = BeautifulSoup(response.text, 'html.parser')

                # 根据提供的HTML结构解析
                hot_items = []

                # 查找包含热榜数据的table
                table = soup.find('table', class_='table')
                if not table:
                    print("未找到热榜表格，可能页面结构已变化")
                    # 尝试查找其他可能的结构
                    rank_items = soup.find_all('div', class_='rank-all-item')
                    if rank_items:
                        print("找到rank-all-item结构")
                        return self._parse_rank_items(rank_items)
                    return []
            
            # 解析每一行数据
            rows = table.find('tbody').find_all('tr')
            
            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        # 排名
                        rank = cells[0].get_text(strip=True).replace('.', '')
                        
                        # 标题和链接
                        title_link = cells[1].find('a')
                        if title_link:
                            title = title_link.get_text(strip=True)
                            link = title_link.get('href', '')
                            itemid = title_link.get('itemid', '')
                        else:
                            continue
                        
                        # 热度值
                        hot_value = cells[2].get_text(strip=True)
                        
                        hot_item = {
                            'rank': int(rank) if rank.isdigit() else 0,
                            'title': title,
                            'hot_value': hot_value,
                            'link': link,
                            'itemid': itemid,
                            'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        hot_items.append(hot_item)

                except Exception as e:
                    print(f"解析行数据出错: {e}")
                    continue

                return hot_items

            except requests.RequestException as e:
                print(f"请求出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(3, 8))
                    continue
                return []
            except Exception as e:
                print(f"解析出错: {e}")
                return []

        return []

    def _parse_rank_items(self, rank_items):
        """解析rank-all-item结构的数据"""
        hot_items = []

        for item in rank_items:
            try:
                table = item.find('table', class_='table')
                if not table:
                    continue

                rows = table.find('tbody').find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        rank = cells[0].get_text(strip=True).replace('.', '')

                        title_link = cells[1].find('a')
                        if title_link:
                            title = title_link.get_text(strip=True)
                            link = title_link.get('href', '')
                            itemid = title_link.get('itemid', '')
                        else:
                            continue

                        hot_value = cells[2].get_text(strip=True)

                        hot_item = {
                            'rank': int(rank) if rank.isdigit() else 0,
                            'title': title,
                            'hot_value': hot_value,
                            'link': link,
                            'itemid': itemid,
                            'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }

                        hot_items.append(hot_item)

            except Exception as e:
                print(f"解析rank-item出错: {e}")
                continue

        return hot_items

    def get_hot_list_selenium(self, url):
        """使用Selenium获取数据（更强的反爬能力）"""
        if not SELENIUM_AVAILABLE:
            print("Selenium未安装，请使用: pip install selenium")
            return []

        options = Options()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument(f'--user-agent={random.choice(self.user_agents)}')

        driver = None
        try:
            print("启动Selenium浏览器...")
            driver = webdriver.Chrome(options=options)
            driver.get(url)

            # 等待页面加载
            time.sleep(random.uniform(3, 6))

            # 检查是否有验证码
            if '安全验证' in driver.page_source:
                print("遇到验证码页面，Selenium也无法自动处理")
                return []

            # 等待表格加载
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "table"))
                )
            except:
                print("等待页面元素超时，尝试解析当前页面")

            # 获取页面源码并解析
            soup = BeautifulSoup(driver.page_source, 'html.parser')

            # 使用相同的解析逻辑
            table = soup.find('table', class_='table')
            if table:
                return self._parse_table_data(table)
            else:
                rank_items = soup.find_all('div', class_='rank-all-item')
                if rank_items:
                    return self._parse_rank_items(rank_items)
                return []

        except Exception as e:
            print(f"Selenium爬取出错: {e}")
            return []
        finally:
            if driver:
                driver.quit()

    def _parse_table_data(self, table):
        """解析表格数据的通用方法"""
        hot_items = []

        tbody = table.find('tbody')
        if not tbody:
            return []

        rows = tbody.find_all('tr')

        for row in rows:
            try:
                cells = row.find_all('td')
                if len(cells) >= 3:
                    rank = cells[0].get_text(strip=True).replace('.', '')

                    title_link = cells[1].find('a')
                    if title_link:
                        title = title_link.get_text(strip=True)
                        link = title_link.get('href', '')
                        itemid = title_link.get('itemid', '')
                    else:
                        continue

                    hot_value = cells[2].get_text(strip=True)

                    hot_item = {
                        'rank': int(rank) if rank.isdigit() else 0,
                        'title': title,
                        'hot_value': hot_value,
                        'link': link,
                        'itemid': itemid,
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    hot_items.append(hot_item)

            except Exception as e:
                print(f"解析行数据出错: {e}")
                continue

        return hot_items

    def save_to_json(self, data, filename=None):
        """
        保存数据到JSON文件
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'sogou_hot_{timestamp}.json'
        
        # 创建保存目录
        save_dir = datetime.now().strftime('%Y')
        os.makedirs(save_dir, exist_ok=True)
        filepath = os.path.join(save_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到: {filepath}")
        return filepath
    
    def save_to_csv(self, data, filename=None):
        """
        保存数据到CSV文件
        """
        import csv
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'sogou_hot_{timestamp}.csv'
        
        save_dir = datetime.now().strftime('%Y')
        os.makedirs(save_dir, exist_ok=True)
        filepath = os.path.join(save_dir, filename)
        
        if data:
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            
            print(f"CSV数据已保存到: {filepath}")
        return filepath
    
    def print_hot_list(self, data):
        """
        打印热榜数据
        """
        print(f"\n{'='*60}")
        print(f"搜狗热榜 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        for item in data:
            print(f"{item['rank']:2d}. {item['title']}")
            print(f"    热度: {item['hot_value']}")
            print(f"    链接: {item['link'][:80]}...")
            print()


def main():
    """
    主函数 - 支持多种爬取方式
    """
    url = "https://tophub.today/n/NaEdZndrOM"

    crawler = SogouHotCrawler()

    print("开始爬取热榜数据...")
    print("=" * 50)

    # 方式1: 使用requests (默认)
    print("🔄 尝试方式1: 使用requests爬取...")
    hot_data = crawler.get_hot_list(url)

    # 如果requests失败，尝试Selenium
    if not hot_data and SELENIUM_AVAILABLE:
        print("\n🔄 方式1失败，尝试方式2: 使用Selenium爬取...")
        hot_data = crawler.get_hot_list_selenium(url)

    # 如果都失败了，给出建议
    if not hot_data:
        print("\n❌ 所有方式都失败了，可能的原因:")
        print("1. 网站有严格的反爬虫保护")
        print("2. 需要人工验证码验证")
        print("3. IP被临时封禁")
        print("\n💡 建议:")
        print("- 等待一段时间后重试")
        print("- 使用代理IP")
        print("- 手动访问网站完成验证后再试")
        print("- 考虑爬取其他数据源")
        return

    print(f"\n✅ 成功爬取 {len(hot_data)} 条热榜数据")

    # 打印数据
    crawler.print_hot_list(hot_data)

    # 保存为JSON
    json_file = crawler.save_to_json(hot_data)

    # 保存为CSV
    csv_file = crawler.save_to_csv(hot_data)

    print(f"\n📁 数据已保存:")
    print(f"   JSON: {json_file}")
    print(f"   CSV:  {csv_file}")


def test_alternative_sources():
    """
    测试其他热榜数据源
    """
    print("🔍 测试其他热榜数据源...")

    # 其他可能的数据源
    sources = {
        '微博热搜': 'https://s.weibo.com/top/summary',
        '百度热搜': 'https://top.baidu.com/board?tab=realtime',
        '知乎热榜': 'https://www.zhihu.com/hot',
    }

    crawler = SogouHotCrawler()

    for name, url in sources.items():
        print(f"\n测试 {name}: {url}")
        try:
            response = crawler.session.get(url, headers=crawler.get_random_headers(), timeout=10)
            print(f"  状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"  ✅ {name} 可以访问")
            else:
                print(f"  ❌ {name} 访问失败")
        except Exception as e:
            print(f"  ❌ {name} 访问出错: {e}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_alternative_sources()
    else:
        main()


if __name__ == "__main__":
    main()
