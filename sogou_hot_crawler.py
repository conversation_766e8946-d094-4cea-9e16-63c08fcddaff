#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜狗热榜爬虫
基于提供的HTML结构分析编写
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from datetime import datetime
import os


class SogouHotCrawler:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_hot_list(self, url):
        """
        爬取热榜数据
        """
        try:
            # 随机延时，避免被反爬
            time.sleep(random.uniform(1, 3))
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 根据提供的HTML结构解析
            hot_items = []
            
            # 查找包含热榜数据的table
            table = soup.find('table', class_='table')
            if not table:
                print("未找到热榜表格")
                return []
            
            # 解析每一行数据
            rows = table.find('tbody').find_all('tr')
            
            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        # 排名
                        rank = cells[0].get_text(strip=True).replace('.', '')
                        
                        # 标题和链接
                        title_link = cells[1].find('a')
                        if title_link:
                            title = title_link.get_text(strip=True)
                            link = title_link.get('href', '')
                            itemid = title_link.get('itemid', '')
                        else:
                            continue
                        
                        # 热度值
                        hot_value = cells[2].get_text(strip=True)
                        
                        hot_item = {
                            'rank': int(rank) if rank.isdigit() else 0,
                            'title': title,
                            'hot_value': hot_value,
                            'link': link,
                            'itemid': itemid,
                            'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
                        
                        hot_items.append(hot_item)
                        
                except Exception as e:
                    print(f"解析行数据出错: {e}")
                    continue
            
            return hot_items
            
        except requests.RequestException as e:
            print(f"请求出错: {e}")
            return []
        except Exception as e:
            print(f"解析出错: {e}")
            return []
    
    def save_to_json(self, data, filename=None):
        """
        保存数据到JSON文件
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'sogou_hot_{timestamp}.json'
        
        # 创建保存目录
        save_dir = datetime.now().strftime('%Y')
        os.makedirs(save_dir, exist_ok=True)
        filepath = os.path.join(save_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到: {filepath}")
        return filepath
    
    def save_to_csv(self, data, filename=None):
        """
        保存数据到CSV文件
        """
        import csv
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'sogou_hot_{timestamp}.csv'
        
        save_dir = datetime.now().strftime('%Y')
        os.makedirs(save_dir, exist_ok=True)
        filepath = os.path.join(save_dir, filename)
        
        if data:
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            
            print(f"CSV数据已保存到: {filepath}")
        return filepath
    
    def print_hot_list(self, data):
        """
        打印热榜数据
        """
        print(f"\n{'='*60}")
        print(f"搜狗热榜 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        for item in data:
            print(f"{item['rank']:2d}. {item['title']}")
            print(f"    热度: {item['hot_value']}")
            print(f"    链接: {item['link'][:80]}...")
            print()


def main():
    """
    主函数 - 示例用法
    """
    # 这里需要替换为实际的搜狗热榜URL
    # 由于无法直接访问，这里提供框架代码
    url = "https://example.com/sogou-hot"  # 替换为实际URL
    
    crawler = SogouHotCrawler()
    
    print("开始爬取搜狗热榜...")
    hot_data = crawler.get_hot_list(url)
    
    if hot_data:
        print(f"成功爬取 {len(hot_data)} 条热榜数据")
        
        # 打印数据
        crawler.print_hot_list(hot_data)
        
        # 保存为JSON
        crawler.save_to_json(hot_data)
        
        # 保存为CSV
        crawler.save_to_csv(hot_data)
        
    else:
        print("未获取到数据")


if __name__ == "__main__":
    main()
