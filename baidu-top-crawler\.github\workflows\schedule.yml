# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Crawl Baidu Top

on:
  schedule:
    - cron: "0 * * * *"

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python 3.8
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

    - name: Run crawl_baidu_top.py
      run: |
        python crawl_baidu_top.py
    # Runs a set of commands using the runners shell
    - name: push to origin master
      run: |
        echo start push
        git config --global user.name "quarrying"
        git config --global user.email "<EMAIL>"
        
        git add -A
        git commit -m $(date '+%Y%m%d_%H%M')
        git push