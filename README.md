# 搜狗热榜爬虫

一个基于Python的搜狗热榜数据爬取工具，支持实时获取热点新闻数据并保存为多种格式。

## 项目特点

- 🚀 **高效爬取**: 基于requests和BeautifulSoup，稳定可靠
- 📊 **多格式输出**: 支持JSON、CSV格式保存
- 🛡️ **反爬保护**: 内置随机延时、User-Agent轮换等反反爬机制
- 📁 **自动分类**: 按年份自动创建文件夹，便于数据管理
- 🔄 **易于扩展**: 模块化设计，方便添加新的数据源

## 项目结构

```
├── sogou_hot_crawler.py    # 主爬虫程序
├── README.md              # 项目说明文档
├── requirements.txt       # 依赖包列表
└── 2025/                 # 数据保存目录（自动创建）
    ├── sogou_hot_20250706_143022.json
    └── sogou_hot_20250706_143022.csv
```

## 安装依赖

```bash
pip install -r requirements.txt
```

或手动安装：

```bash
pip install requests beautifulsoup4 lxml
```

## 使用方法

### 基本用法

```python
from sogou_hot_crawler import SogouHotCrawler

# 创建爬虫实例
crawler = SogouHotCrawler()

# 爬取热榜数据
url = "你的目标URL"
hot_data = crawler.get_hot_list(url)

# 保存数据
crawler.save_to_json(hot_data)
crawler.save_to_csv(hot_data)
```

### 命令行运行

```bash
python sogou_hot_crawler.py
```

### 定时爬取

可以结合crontab或Windows任务计划程序实现定时爬取：

```bash
# 每30分钟执行一次
*/30 * * * * /usr/bin/python3 /path/to/sogou_hot_crawler.py
```

## 数据格式

### JSON格式示例

```json
[
  {
    "rank": 1,
    "title": "幼童躺街上被快递货车碾压身亡",
    "hot_value": "599.6万",
    "link": "https://www.sogou.com/web?ie=utf8&query=...",
    "itemid": "211473089",
    "crawl_time": "2025-07-06 14:30:22"
  }
]
```

### CSV格式

| rank | title | hot_value | link | itemid | crawl_time |
|------|-------|-----------|------|--------|------------|
| 1 | 幼童躺街上被快递货车碾压身亡 | 599.6万 | https://... | 211473089 | 2025-07-06 14:30:22 |

## 配置说明

### 请求头配置

```python
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)...',
    'Accept': 'text/html,application/xhtml+xml...',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    # 更多配置...
}
```

### 反爬策略

- **随机延时**: 1-3秒随机间隔
- **Session复用**: 保持连接状态
- **异常处理**: 完善的错误处理机制
- **超时设置**: 10秒请求超时

## 爬取频率建议

| 场景 | 建议频率 | 说明 |
|------|----------|------|
| 实时监控 | 5-10分钟 | 适合紧急事件追踪 |
| 日常监控 | 15-30分钟 | **推荐**，平衡效率与资源 |
| 趋势分析 | 1小时 | 适合长期数据收集 |
| 研究用途 | 2-6小时 | 减少服务器压力 |

## 注意事项

### 法律合规
- ⚠️ 请遵守网站robots.txt协议
- ⚠️ 不要过于频繁地请求，避免给服务器造成压力
- ⚠️ 爬取的数据仅供个人学习研究使用

### 技术限制
- 部分网站可能有反爬虫保护
- 网站结构变化可能导致爬虫失效
- 需要根据实际情况调整解析规则

### 错误处理
- 网络异常自动重试
- 解析失败跳过当前项
- 详细的错误日志输出

## 扩展功能

### 添加新的数据源

```python
def crawl_new_source(self, url):
    # 实现新的爬取逻辑
    pass
```

### 数据库存储

```python
def save_to_database(self, data):
    # 实现数据库存储逻辑
    pass
```

### 实时推送

```python
def send_notification(self, hot_items):
    # 实现消息推送逻辑
    pass
```

## 常见问题

### Q: 爬虫返回403错误怎么办？
A: 可能触发了反爬机制，建议：
- 增加请求间隔时间
- 更换User-Agent
- 使用代理IP

### Q: 如何处理动态加载的内容？
A: 可以考虑使用Selenium：
```bash
pip install selenium
```

### Q: 数据格式解析失败怎么办？
A: 检查网站是否更新了HTML结构，相应调整解析代码。

## 更新日志

- **v1.0.0** (2025-07-06)
  - 初始版本发布
  - 支持基本的热榜数据爬取
  - 支持JSON、CSV格式输出

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
